import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { updateExistingMiningUnitsROI } from '@/lib/mining';
import { systemLogDb } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    console.log(`Mining units ROI update initiated by admin: ${session.user.email}`);

    // Update existing mining units with new ROI configuration
    const result = await updateExistingMiningUnitsROI();

    // Log the manual trigger
    await systemLogDb.create({
      action: 'MANUAL_MINING_UNITS_ROI_UPDATE_TRIGGERED',
      details: {
        triggeredBy: session.user.email,
        adminId: session.user.id,
        result,
        timestamp: new Date().toISOString(),
      },
    });

    console.log(`Mining units ROI update completed. Updated ${result.unitsUpdated} out of ${result.totalUnits} units`);

    return NextResponse.json({
      success: true,
      message: 'Mining units ROI update completed successfully',
      data: {
        unitsUpdated: result.unitsUpdated,
        totalUnits: result.totalUnits,
        updateResults: result.updateResults,
      },
    });

  } catch (error: any) {
    console.error('Mining units ROI update error:', error);
    
    // Log the error
    try {
      const session = await getServerSession(authOptions);
      await systemLogDb.create({
        action: 'MANUAL_MINING_UNITS_ROI_UPDATE_ERROR',
        details: {
          triggeredBy: session?.user?.email || 'unknown',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (logError) {
      console.error('Failed to log mining units ROI update error:', logError);
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to update mining units ROI' },
      { status: 500 }
    );
  }
}
