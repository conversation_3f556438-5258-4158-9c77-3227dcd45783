'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent, Button, Input } from '@/components/ui';
import { 
  Settings, 
  Save, 
  DollarSign, 
  Percent, 
  Zap,
  Users,
  Shield,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface SystemSettings {
  // Mining Unit Pricing
  thsPriceUSD: number;
  minPurchaseAmount: number;
  maxPurchaseAmount: number;

  // Earnings Configuration
  roiSmallUnitsMin: number;
  roiSmallUnitsMax: number;
  roiMediumUnitsMin: number;
  roiMediumUnitsMax: number;
  roiLargeUnitsMin: number;
  roiLargeUnitsMax: number;
  monthlyReturnMin: number;
  monthlyReturnMax: number;
  binaryBonusPercentage: number;
  referralBonusPercentage: number;

  // Deposit Settings
  usdtDepositAddress: string;
  minDepositAmount: number;
  maxDepositAmount: number;
  depositEnabled: boolean;
  minConfirmations: number;
  depositFeePercentage: number;

  // Withdrawal Settings
  minWithdrawalAmount: number;
  withdrawalFeeFixed: number;
  withdrawalFeePercentage: number;
  withdrawalProcessingDays: number;

  // Platform Settings
  platformFeePercentage: number;
  maintenanceMode: boolean;
  registrationEnabled: boolean;
  kycRequired: boolean;
}

export const SystemSettings: React.FC = () => {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/settings', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSettings(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!settings) return;

    try {
      setSaving(true);
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        setSaveSuccess(true);
        setTimeout(() => setSaveSuccess(false), 3000);
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (key: keyof SystemSettings, value: any) => {
    if (!settings) return;
    setSettings({ ...settings, [key]: value });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-700 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-slate-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">Failed to Load Settings</h3>
        <p className="text-slate-400">Unable to load system settings. Please try again.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">System Settings</h1>
          <p className="text-slate-400 mt-1">Configure platform parameters and business rules</p>
        </div>
        <Button
          onClick={handleSave}
          loading={saving}
          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
        >
          {saveSuccess ? (
            <>
              <CheckCircle className="h-4 w-4" />
              Saved
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              Save Changes
            </>
          )}
        </Button>
      </div>

      {/* Mining Unit Pricing */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Zap className="h-5 w-5 text-blue-400" />
            Mining Unit Pricing
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                THS Price (USD)
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.thsPriceUSD}
                  onChange={(e) => updateSetting('thsPriceUSD', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Minimum Purchase
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.minPurchaseAmount}
                  onChange={(e) => updateSetting('minPurchaseAmount', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Maximum Purchase
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.maxPurchaseAmount}
                  onChange={(e) => updateSetting('maxPurchaseAmount', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Earnings Configuration */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Percent className="h-5 w-5 text-orange-400" />
            Earnings Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Daily ROI (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.dailyROIPercentage}
                  onChange={(e) => updateSetting('dailyROIPercentage', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Binary Bonus (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.binaryBonusPercentage}
                  onChange={(e) => updateSetting('binaryBonusPercentage', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Referral Bonus (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.referralBonusPercentage}
                  onChange={(e) => updateSetting('referralBonusPercentage', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Withdrawal Settings */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <DollarSign className="h-5 w-5 text-red-400" />
            Withdrawal Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Minimum Withdrawal
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.minWithdrawalAmount}
                  onChange={(e) => updateSetting('minWithdrawalAmount', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Fixed Fee ($)
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.withdrawalFeeFixed}
                  onChange={(e) => updateSetting('withdrawalFeeFixed', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Percentage Fee (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.withdrawalFeePercentage}
                  onChange={(e) => updateSetting('withdrawalFeePercentage', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Processing Days
              </label>
              <Input
                type="number"
                value={settings.withdrawalProcessingDays}
                onChange={(e) => updateSetting('withdrawalProcessingDays', parseInt(e.target.value) || 0)}
                className="bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"
              />
            </div>
          </div>

          <div className="mt-4 p-4 bg-slate-700 rounded-lg">
            <h4 className="text-sm font-medium text-slate-300 mb-2">Fee Calculation Example:</h4>
            <p className="text-xs text-slate-400">
              For a $100 withdrawal: Fixed Fee (${settings.withdrawalFeeFixed}) + Percentage Fee (${(100 * (settings.withdrawalFeePercentage / 100)).toFixed(2)}) = Total Fee: ${(settings.withdrawalFeeFixed + (100 * (settings.withdrawalFeePercentage / 100))).toFixed(2)}
            </p>
            <p className="text-xs text-slate-400 mt-1">
              User receives: ${(100 - (settings.withdrawalFeeFixed + (100 * (settings.withdrawalFeePercentage / 100)))).toFixed(2)}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Deposit Settings */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <DollarSign className="h-5 w-5 text-green-400" />
            Deposit Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                USDT TRC20 Deposit Address
              </label>
              <Input
                type="text"
                value={settings.usdtDepositAddress}
                onChange={(e) => updateSetting('usdtDepositAddress', e.target.value)}
                placeholder="Enter USDT TRC20 address (e.g., TXXXxxxXXXxxxXXX...)"
                className="bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500 font-mono text-sm"
              />
              <p className="text-xs text-slate-400 mt-1">
                This address will be displayed to users for USDT deposits
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Minimum Deposit
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.minDepositAmount}
                  onChange={(e) => updateSetting('minDepositAmount', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Maximum Deposit
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.maxDepositAmount}
                  onChange={(e) => updateSetting('maxDepositAmount', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Deposit Fee (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.depositFeePercentage}
                  onChange={(e) => updateSetting('depositFeePercentage', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Minimum Confirmations
              </label>
              <Input
                type="number"
                value={settings.minConfirmations}
                onChange={(e) => updateSetting('minConfirmations', parseInt(e.target.value) || 0)}
                className="bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"
              />
              <p className="text-xs text-slate-400 mt-1">
                Number of blockchain confirmations required
              </p>
            </div>
            <div className="flex items-center justify-between pt-6">
              <div>
                <h4 className="text-sm font-medium text-white">Deposits Enabled</h4>
                <p className="text-sm text-slate-400">Allow users to make deposits</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.depositEnabled}
                  onChange={(e) => updateSetting('depositEnabled', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
              </label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Platform Settings */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Settings className="h-5 w-5 text-blue-400" />
            Platform Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Platform Fee (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.platformFeePercentage}
                  onChange={(e) => updateSetting('platformFeePercentage', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          <div className="space-y-4 pt-4 border-t border-slate-600">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-white">Maintenance Mode</h4>
                <p className="text-sm text-slate-400">Temporarily disable platform access</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.maintenanceMode}
                  onChange={(e) => updateSetting('maintenanceMode', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-white">Registration Enabled</h4>
                <p className="text-sm text-slate-400">Allow new user registrations</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.registrationEnabled}
                  onChange={(e) => updateSetting('registrationEnabled', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-white">KYC Required</h4>
                <p className="text-sm text-slate-400">Require KYC verification for withdrawals</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.kycRequired}
                  onChange={(e) => updateSetting('kycRequired', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
